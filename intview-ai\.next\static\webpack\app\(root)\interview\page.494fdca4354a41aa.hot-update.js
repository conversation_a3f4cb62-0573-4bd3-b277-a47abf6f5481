"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(root)/interview/page",{

/***/ "(app-pages-browser)/./lib/interview-api.ts":
/*!******************************!*\
  !*** ./lib/interview-api.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   interviewApi: () => (/* binding */ interviewApi)\n/* harmony export */ });\nclass InterviewApiService {\n    async sendInterviewRequest(request) {\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/interview\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(request)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(\"Interview API Error: \".concat(response.status, \" \").concat(response.statusText, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Failed to send interview request:', error);\n            throw error;\n        }\n    }\n    async startInterview(position, name, experience) {\n        const request = {\n            position,\n            name,\n            experience,\n            history: []\n        };\n        const response = await this.sendInterviewRequest(request);\n        return response;\n    }\n    async continueInterview(position, name, experience, history) {\n        const request = {\n            position,\n            name,\n            experience,\n            history\n        };\n        const response = await this.sendInterviewRequest(request);\n        return response;\n    }\n    async analyzeVideoTranscript(transcript) {\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/interview/analyze-video\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    transcript\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(\"Video Analysis API Error: \".concat(response.status, \" \").concat(response.statusText, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            console.error('Failed to analyze video transcript:', error);\n            throw error;\n        }\n    }\n    constructor(){\n        this.baseUrl = 'https://interview-server-delta.vercel.app';\n    }\n}\n// Export singleton instance\nconst interviewApi = new InterviewApiService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/interview-api.ts\n"));

/***/ })

});