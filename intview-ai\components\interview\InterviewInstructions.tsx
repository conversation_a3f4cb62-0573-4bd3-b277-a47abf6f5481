"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

import {
  content,
  responsibilities,
  requirements,
  preferences,
  defaultInstructions,
  defaultEnvironment,
  defaultDisclaimers,
} from "@/lib/interviewinstructions";

import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, DollarSign, MapPin, Briefcase } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

type InterviewPanelProps = {
  mode: "apply" | "instructions";
  title?: string;
  jobeprice?: string;
  location?: string;
  jobType?: string;
  onBack?: () => void;
  candidateName?: string;
  jobTitle?: string;
  languages?: string[];
  instructions?: string[];
  environmentChecklist?: string[];
  disclaimers?: string[];
  onNext?: () => void;
  onApplied?: () => void;
};

const InterviewInstructions: React.FC<InterviewPanelProps> = ({
  mode,
  title = "Untitled Role",
  jobeprice = "N/A",
  location = "Remote",
  jobType = "Full-time",
  onBack,
  candidateName = "Jonathan",
  jobTitle = "Insurance Agent",
  languages = ["English", "Chinese"],
  instructions = defaultInstructions,
  environmentChecklist = defaultEnvironment,
  disclaimers = defaultDisclaimers,
  onNext,
  onApplied,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const router = useRouter();

  const isInstructions = mode === "instructions";

  return (
    <div className="flex-1 border border-gray-300 rounded-md bg-white p-6">
      <div className="flex flex-col gap-6 text-[#38383a]">
        {isInstructions ? (
          <p className="font-semibold text-xl">Instructions for Interview!</p>
        ) : (
          <p className="font-semibold text-xl">Job Post Company Name</p>
        )}

        {isInstructions ? (
          <>
            <div>
              {/* Greeting */}
              <p className="font-normal text-lg mb-4">
                Dear {candidateName},
              </p>

              {/* Role intro */}
              <p className="text-sm mb-4">
                As part of the process, you are required to complete an AI video
                assessment for the role of {jobTitle}.
              </p>

              <Section title="Languages" items={languages} />

            </div>

            <Section title="Instructions" items={instructions} />
            <Section
              title="Environment Checklist"
              items={environmentChecklist}
            />
            <Section title="Important Disclaimers" items={disclaimers} />

            <div className="flex items-start gap-2 mt-6">
              <input
                type="checkbox"
                id="terms"
                checked={isChecked}
                onChange={(e) => setIsChecked(e.target.checked)}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="terms" className="text-[11px] text-[#38383a]">
                By checking this box, you agree with AI Interview{" "}
                <span className="text-primary cursor-pointer font-medium">
                  Terms of use
                </span>
                .
              </label>
            </div>
          </>
        ) : (
          <>
            <div>
              <h2 className="text-xl font-semibold mb-1">{title}</h2>
              <p className="text-sm text-[#000000] flex flex-wrap gap-2">
                <span className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  {jobeprice}
                </span>
                <span>&bull;</span>
                <span className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {location}
                </span>
                <span>&bull;</span>
                <span className="flex items-center gap-1">
                  <Briefcase className="h-4 w-4" />
                  {jobType}
                </span>
              </p>
            </div>

            <p className="text-sm text-gray-700">{content}</p>
            <Section title="Responsibilities" items={responsibilities} />
            <Section title="Requirements" items={requirements} />
            <Section title="Preferences" items={preferences} />
          </>
        )}

        <div className="flex justify-center">
          <Button
            disabled={isInstructions && !isChecked}
            onClick={() => {
              if (isInstructions) {
                onNext?.();
              } else {
                setIsDialogOpen(true);
                onApplied?.();
              }
            }}
            className={`py-3 hover:cursor-pointer text-sm rounded-full w-full sm:w-[200px] lg:w-[300px] flex items-center justify-center gap-2 group text-white ${
              isInstructions ? "bg-primary" : "bg-[#6938EF]"
            }`}
          >
            {isInstructions ? "Start Interview" : "Apply now"}
            <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>

      {/* Always keep Dialog mounted so it can open */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="w-full max-w-md text-center rounded-xl bg-white py-6 px-6">
          <DialogHeader>
            <DialogTitle className="sr-only">
              Job application successful
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col items-center space-y-5">
            <div className="bg-gray-100 rounded-full relative">
              <Image
                src="/images/tick.png"
                alt="Success Icon"
                width={55}
                height={55}
              />
              <span className="absolute top-2 right-13 w-[14px] h-[14px] bg-purple-500 rounded-full" />
              <span className="absolute top-0 left-12 w-[18px] h-[18px] bg-green-400 rounded-full" />
              <span className="absolute bottom-0 left-12 w-[12px] h-[12px] bg-blue-400 rounded-full" />
            </div>

            <h2 className="text-[28px] font-bold text-black text-center">
              You have successfully applied for this job!
            </h2>

            <p className="italic text-[18px] text-gray-500 px-2">
              Great news! Your application is now in review. The next step is to
              await approval for your AI interview.
            </p>

            <button
              onClick={() => {
                setIsDialogOpen(false);
                onBack?.();
                setTimeout(() => {
                  router.push("/job-posts");
                }, 100);
              }}
              className="bg-[#6938EF] hover:cursor-pointer w-full text-white px-6 py-3 rounded-full font-semibold hover:opacity-90 transition"
            >
              Go to Job Post
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const Section = ({ title, items }: { title: string; items: string[] }) => (
  <div>
    <h3 className="font-semibold text-lg mb-2">{title}:</h3>
    <ul className="list-disc list-inside space-y-2 text-sm">
      {items.map((item, i) => (
        <li key={i}>{item}</li>
      ))}
    </ul>
  </div>
);

export default InterviewInstructions;
