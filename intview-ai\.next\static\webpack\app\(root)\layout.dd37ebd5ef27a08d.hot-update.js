"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(root)/layout",{

/***/ "(app-pages-browser)/./components/sideBar.tsx":
/*!********************************!*\
  !*** ./components/sideBar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _public_images_logo_light_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/logo-light.svg */ \"(app-pages-browser)/./public/images/logo-light.svg\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusiness,LayoutDashboard,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusiness,LayoutDashboard,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase-business.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusiness,LayoutDashboard,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst sidebarItems = [\n    {\n        label: \"\",\n        href: \"/sign-in\",\n        icon: _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        label: \"Job Posts\",\n        href: \"/interview\",\n        icon: _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    }\n];\nconst Sidebar = (param)=>{\n    let { isOpen, onClose } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const previousPathname = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(pathname);\n    // Close mobile sidebar when route changes (but not on initial mount)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (previousPathname.current !== pathname && isOpen && onClose) {\n                onClose();\n            }\n            previousPathname.current = pathname;\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        isOpen,\n        onClose\n    ]);\n    // Close mobile sidebar when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Sidebar.useEffect.handleClickOutside\": (event)=>{\n                    const sidebar = document.getElementById(\"mobile-sidebar\");\n                    const overlay = document.getElementById(\"sidebar-overlay\");\n                    if (sidebar && !sidebar.contains(event.target) && (overlay === null || overlay === void 0 ? void 0 : overlay.contains(event.target))) {\n                        if (onClose) onClose();\n                    }\n                }\n            }[\"Sidebar.useEffect.handleClickOutside\"];\n            if (isOpen) {\n                document.addEventListener(\"mousedown\", handleClickOutside);\n                document.body.style.overflow = \"hidden\";\n            } else {\n                document.body.style.overflow = \"unset\";\n            }\n            return ({\n                \"Sidebar.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                    document.body.style.overflow = \"unset\";\n                }\n            })[\"Sidebar.useEffect\"];\n        }\n    }[\"Sidebar.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"hidden lg:flex w-54 h-full bg-white border-r p-6 flex-col shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: _public_images_logo_light_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                            alt: \"Logo\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col gap-4\",\n                        children: sidebarItems.map((item)=>{\n                            const isActive = pathname === item.href;\n                            const Icon = item.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                className: \"flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\\n                  \".concat(isActive ? \"bg-purple-100 text-purple-700 font-extrabold\" : \"text-gray-400 hover:bg-gray-50 hover:text-gray-600\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5 \".concat(isActive ? \"text-purple-700\" : \"text-gray-400\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, item.label, true, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"sidebar-overlay\",\n                className: \"fixed inset-0 bg-gray-300/50 bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 \".concat(isOpen ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                    id: \"mobile-sidebar\",\n                    className: \"fixed left-0 top-0 h-full w-64 bg-white border-r p-6 flex flex-col z-50 transform transition-transform duration-300 ease-in-out \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: _public_images_logo_light_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    alt: \"Logo\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col gap-4\",\n                            children: sidebarItems.map((item)=>{\n                                const isActive = pathname === item.href;\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: \"flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\\n                      \".concat(isActive ? \"bg-purple-100 text-purple-700 font-extrabold\" : \"text-gray-400 hover:bg-gray-50 hover:text-gray-600\"),\n                                    onClick: onClose,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-5 h-5 \".concat(isActive ? \"text-purple-700\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, item.label, true, {\n                                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Sidebar, \"vogvo3qltD2od2YqdQd0J7qOFRU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sideBar.tsx\n"));

/***/ })

});