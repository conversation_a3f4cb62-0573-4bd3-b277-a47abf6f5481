"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(middleware)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/github */ \"(middleware)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(middleware)/./node_modules/next-auth/providers/google.js\");\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    providers: [\n        next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ],\n    secret: process.env.AUTH_SECRET,\n    pages: {\n        signIn: \"/sign-in\",\n        signUp: \"/sign-up\"\n    },\n    callbacks: {\n        authorized ({ auth, request: { nextUrl } }) {\n            const isLoggedIn = !!auth?.user;\n            const isOnSignIn = nextUrl.pathname.startsWith(\"/sign-in\");\n            const isOnSignUp = nextUrl.pathname.startsWith(\"/sign-up\");\n            const isOnAuth = isOnSignIn || isOnSignUp;\n            // If user is logged in and trying to access auth pages, redirect to job posts\n            if (isLoggedIn && isOnAuth) {\n                return Response.redirect(new URL(\"/job-posts\", nextUrl));\n            }\n            // If user is not logged in and not on auth pages, redirect to sign-in\n            if (!isLoggedIn && !isOnAuth) {\n                return Response.redirect(new URL(\"/sign-in\", nextUrl));\n            }\n            return true;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Redirect to job posts after successful authentication\n            if (url === baseUrl || url === `${baseUrl}/`) {\n                return `${baseUrl}/job-posts`;\n            }\n            // Allow relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            // Allow callback URLs on the same origin\n            if (new URL(url).origin === baseUrl) return url;\n            return `${baseUrl}/job-posts`;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./auth.ts\n");

/***/ })

});