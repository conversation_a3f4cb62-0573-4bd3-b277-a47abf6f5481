"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(middleware)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/github */ \"(middleware)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(middleware)/./node_modules/next-auth/providers/google.js\");\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    providers: [\n        next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ],\n    secret: process.env.AUTH_SECRET,\n    pages: {\n        signIn: \"/sign-in\",\n        signUp: \"/sign-up\"\n    },\n    callbacks: {\n        authorized ({ auth, request: { nextUrl } }) {\n            const isLoggedIn = !!auth?.user;\n            const isOnSignIn = nextUrl.pathname.startsWith(\"/sign-in\");\n            const isOnSignUp = nextUrl.pathname.startsWith(\"/sign-up\");\n            const isOnAuth = isOnSignIn || isOnSignUp;\n            // If user is logged in and trying to access auth pages, redirect to home\n            if (isLoggedIn && isOnAuth) {\n                return Response.redirect(new URL(\"/\", nextUrl));\n            }\n            // If user is not logged in and not on auth pages, redirect to sign-in\n            if (!isLoggedIn && !isOnAuth) {\n                return Response.redirect(new URL(\"/sign-in\", nextUrl));\n            }\n            return true;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./auth.ts\n");

/***/ })

});