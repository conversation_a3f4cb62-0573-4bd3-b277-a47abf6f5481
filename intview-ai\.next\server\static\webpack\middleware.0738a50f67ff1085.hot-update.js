"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(middleware)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/github */ \"(middleware)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(middleware)/./node_modules/next-auth/providers/google.js\");\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    providers: [\n        next_auth_providers_github__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ],\n    secret: process.env.AUTH_SECRET,\n    pages: {\n        signIn: \"/sign-in\",\n        signUp: \"/sign-up\"\n    },\n    callbacks: {\n        authorized ({ auth, request: { nextUrl } }) {\n            const isLoggedIn = !!auth?.user;\n            const isOnSignIn = nextUrl.pathname.startsWith(\"/sign-in\");\n            const isOnSignUp = nextUrl.pathname.startsWith(\"/sign-up\");\n            const isOnAuth = isOnSignIn || isOnSignUp;\n            // If user is logged in and trying to access auth pages, redirect to job posts\n            if (isLoggedIn && isOnAuth) {\n                return Response.redirect(new URL(\"/job-posts\", nextUrl));\n            }\n            // If user is not logged in and not on auth pages, redirect to sign-in\n            if (!isLoggedIn && !isOnAuth) {\n                return Response.redirect(new URL(\"/sign-in\", nextUrl));\n            }\n            return true;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Redirect to job posts after successful authentication\n            if (url === baseUrl || url === `${baseUrl}/`) {\n                return `${baseUrl}/job-posts`;\n            }\n            // Allow relative callback URLs\n            if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\n            // Allow callback URLs on the same origin\n            if (new URL(url).origin === baseUrl) return url;\n            return `${baseUrl}/job-posts`;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./auth.ts\n");

/***/ })

});