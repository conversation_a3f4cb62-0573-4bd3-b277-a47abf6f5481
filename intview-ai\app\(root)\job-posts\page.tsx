"use client";

import { Input } from "@/components/ui/input";
import { Search, Briefcase } from "lucide-react";
import { jobData as initialJobs } from "@/lib/jobedata";
import InterviewInstructions from "@/components/interview/InterviewInstructions";
import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const JobPosts = () => {
  // Copy job data into state so we can update isApplied dynamically
  const [jobs, setJobs] = useState(initialJobs);
  const [showApplication, setShowApplication] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [selectedJob, setSelectedJob] = useState<any>(null);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleApplyClick = (job: any) => {
    setSelectedJob(job);
    setShowApplication(true);
  };

  // Mark the selected job as applied
  const handleApplicationComplete = (jobid: string) => {
    setJobs((prevJobs) =>
      prevJobs.map((obj) =>
        obj.id === jobid ? { ...obj, isApplied: true } : obj
      )
    );
  };

  return (
    <>
      {!showApplication ? (
        <div className="flex flex-col gap-7">
          <h1 className="font-poppins font-semibold text-[22px] text-gray-900">
            Recent Job Posts
          </h1>

          {/* Search Input */}
          <div className="relative w-full h-[53px] max-w-[490px]">
            <Input
              type="search"
              placeholder="Search..."
              className="pl-3 pr-10 h-full w-full bg-[#FAFAFA] text-gray-700"
            />
            <Search className="absolute right-4 top-1/2 -translate-y-1/2 text-[#B3B3B3] h-5 w-5" />
          </div>

          {/* Job Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
            {jobs.map((job) => (
              <Card key={job.id} className="shadow-md rounded-xl border p-4">
                <CardHeader className="pb-1 px-0">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-base font-semibold text-gray-900">
                      {job.title}
                    </CardTitle>
                    <span className="rounded-full bg-[#CCFFB1] px-3 py-0.5 text-xs font-medium text-[#144100]">
                      {job.status}
                    </span>
                  </div>
                  <CardDescription className="mt-1 text-sm text-[#000000] flex flex-wrap items-center gap-2">
                    <span>{job.price}</span>
                    <span>&bull;</span>
                    <span className="flex items-center gap-1">
                      <Briefcase className="h-4 w-4" />
                      {job.jobType}
                    </span>
                  </CardDescription>
                </CardHeader>

                <CardContent className="pt-0 px-0 pb-2">
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {job.description}
                  </p>
                </CardContent>

                <CardFooter className="px-0 pt-0 flex gap-2 flex-col">
                  <button
                    className="w-full rounded-full bg-[#6938EF] px-4 py-2.5 text-sm font-semibold hover:cursor-pointer text-white hover:opacity-80 transition disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => handleApplyClick(job)}
                    disabled={job.isApplied}
                  >
                    {job.isApplied ? "Applied" : "Apply Now"}
                  </button>

                  {job.isApplied && (
                    <button
                      className="w-full hover:bg-[#6938EF] hover:cursor-pointer hover:text-white rounded-full border border-[#6938EF] px-4 py-2.5 text-sm font-semibold text-[#6938EF] bg-white hover:opacity-80 transition"
                      onClick={() =>
                        console.log("Start interview for", job.title)
                      }
                    >
                      Interview for this job
                    </button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      ) : (
        <InterviewInstructions
          mode="apply"
          title={selectedJob?.title}
          jobeprice={selectedJob?.price}
          location={selectedJob?.location}
          jobType={selectedJob?.jobType}
          onBack={() => setShowApplication(false)}
          onApplied={() => handleApplicationComplete(selectedJob.id)}
        />
      )}
    </>
  );
};

export default JobPosts;
