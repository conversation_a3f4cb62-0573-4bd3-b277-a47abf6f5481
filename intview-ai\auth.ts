import NextAuth from "next-auth";
import GitHub from "next-auth/providers/github";
import Google from "next-auth/providers/google";

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [GitHub, Google],
  secret: process.env.AUTH_SECRET,
  pages: {
    signIn: "/sign-in",
    signUp: "/sign-up",
  },
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnSignIn = nextUrl.pathname.startsWith("/sign-in");
      const isOnSignUp = nextUrl.pathname.startsWith("/sign-up");
      const isOnAuth = isOnSignIn || isOnSignUp;

      // If user is logged in and trying to access auth pages, redirect to home
      if (isLoggedIn && isOnAuth) {
        return Response.redirect(new URL("/", nextUrl));
      }

      // If user is not logged in and not on auth pages, redirect to sign-in
      if (!isLoggedIn && !isOnAuth) {
        return Response.redirect(new URL("/sign-in", nextUrl));
      }

      return true;
    },
  },
});
