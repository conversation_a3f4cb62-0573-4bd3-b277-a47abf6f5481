import { Bell } from "lucide-react";

interface NotificationItemProps {
  message: string;
  time: string;
}

export default function NotificationItem({
  message,
  time,
}: NotificationItemProps) {
  return (
    <div className="flex items-start gap-3 py-3 border-b last:border-b-0 sm:px-1">
      {/* Icon */}
      <div className="flex-shrink-0 bg-[#F3E8FF] rounded-full p-1 Sm:p-2">
        <Bell className="sm:h-4 sm:w-4 h-3 w-3 " stroke="#6938EF" fill="#6938EF" />
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex flex-col sm:flex-row sm:items-end sm:justify-around gap-1 sm:gap-3">
          <p className="sm:text-sm text-xs text-gray-700 sm:break-words leading-5 flex-1">
            {message}
          </p>
          <p className="sm:text-xs text-xs text-gray-400 sm:whitespace-nowrap sm:flex-shrink-0">
            {time}
          </p>
        </div>
      </div>
    </div>
  );
}
