"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(root)/layout",{

/***/ "(app-pages-browser)/./components/sideBar.tsx":
/*!********************************!*\
  !*** ./components/sideBar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _public_images_logo_light_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../public/images/logo-light.svg */ \"(app-pages-browser)/./public/images/logo-light.svg\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusiness,LayoutDashboard,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusiness,LayoutDashboard,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase-business.js\");\n/* harmony import */ var _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BriefcaseBusiness,LayoutDashboard,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst sidebarItems = [\n    {\n        label: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        label: \"Job Posts\",\n        href: \"/interview\",\n        icon: _barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    }\n];\nconst Sidebar = (param)=>{\n    let { isOpen, onClose } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const previousPathname = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(pathname);\n    // Close mobile sidebar when route changes (but not on initial mount)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (previousPathname.current !== pathname && isOpen && onClose) {\n                onClose();\n            }\n            previousPathname.current = pathname;\n        }\n    }[\"Sidebar.useEffect\"], [\n        pathname,\n        isOpen,\n        onClose\n    ]);\n    // Close mobile sidebar when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"Sidebar.useEffect.handleClickOutside\": (event)=>{\n                    const sidebar = document.getElementById(\"mobile-sidebar\");\n                    const overlay = document.getElementById(\"sidebar-overlay\");\n                    if (sidebar && !sidebar.contains(event.target) && (overlay === null || overlay === void 0 ? void 0 : overlay.contains(event.target))) {\n                        if (onClose) onClose();\n                    }\n                }\n            }[\"Sidebar.useEffect.handleClickOutside\"];\n            if (isOpen) {\n                document.addEventListener(\"mousedown\", handleClickOutside);\n                document.body.style.overflow = \"hidden\";\n            } else {\n                document.body.style.overflow = \"unset\";\n            }\n            return ({\n                \"Sidebar.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                    document.body.style.overflow = \"unset\";\n                }\n            })[\"Sidebar.useEffect\"];\n        }\n    }[\"Sidebar.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"hidden lg:flex w-54 h-full bg-white border-r p-6 flex-col shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: _public_images_logo_light_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                            alt: \"Logo\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col gap-4\",\n                        children: sidebarItems.map((item)=>{\n                            const isActive = pathname === item.href;\n                            const Icon = item.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                className: \"flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\\n                  \".concat(isActive ? \"bg-purple-100 text-purple-700 font-extrabold\" : \"text-gray-400 hover:bg-gray-50 hover:text-gray-600\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-5 h-5 \".concat(isActive ? \"text-purple-700\" : \"text-gray-400\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, item.label, true, {\n                                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"sidebar-overlay\",\n                className: \"fixed inset-0 bg-gray-300/50 bg-opacity-50 z-40 lg:hidden transition-opacity duration-300 \".concat(isOpen ? \"opacity-100\" : \"opacity-0 pointer-events-none\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                    id: \"mobile-sidebar\",\n                    className: \"fixed left-0 top-0 h-full w-64 bg-white border-r p-6 flex flex-col z-50 transform transition-transform duration-300 ease-in-out \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: _public_images_logo_light_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    alt: \"Logo\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BriefcaseBusiness_LayoutDashboard_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col gap-4\",\n                            children: sidebarItems.map((item)=>{\n                                const isActive = pathname === item.href;\n                                const Icon = item.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: \"flex items-center gap-3 px-4 py-3 rounded-lg transition-all group\\n                      \".concat(isActive ? \"bg-purple-100 text-purple-700 font-extrabold\" : \"text-gray-400 hover:bg-gray-50 hover:text-gray-600\"),\n                                    onClick: onClose,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-5 h-5 \".concat(isActive ? \"text-purple-700\" : \"text-gray-400\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, item.label, true, {\n                                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Softwares\\\\Ai bot\\\\intview-ai\\\\components\\\\sideBar.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(Sidebar, \"vogvo3qltD2od2YqdQd0J7qOFRU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sideBar.tsx\n"));

/***/ })

});