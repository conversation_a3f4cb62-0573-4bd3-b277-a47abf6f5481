import ScoreBar from "./ScoreBar";
import CircularRating from "./CircularRating";
import { useInterview } from "@/context/InterviewContext";

const ScoreCard = ({ videoScores = {} }) => {
  const { videoScores: contextVideoScores, isAnalyzingVideo, analyzeVideo } = useInterview();

  // Default values for video scores
  const defaultVideoScores = {
    professionalism: 64,
    energyLevel: 56,
    communication: 58,
    sociability: 70
  };

  // Use context scores if available, otherwise use provided scores, then defaults
  const scores = {
    ...defaultVideoScores,
    ...videoScores,
    ...(contextVideoScores || {})
  };
  return (
    <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto">
      {/* Resume Score */}
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="flex justify-between font-semibold mb-4">
          <span>Resume Score</span>
          <span>65%</span>
        </div>
        <div className="flex flex-col gap-4">
          <ScoreBar label="Company Fit" value={66} />
          <ScoreBar
            label="Relevant Experience"
            value={66}
            color="bg-purple-600"
          />
          <ScoreBar label="Job Knowledge" value={66} />
          <ScoreBar label="Education" value={66} />
          <ScoreBar label="Hard Skills" value={66} />
        </div>

        <div className="mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8">
          Over All Score &nbsp; <span className="text-black">66/100</span>
        </div>
      </div>

      {/* Video Score */}
      <div className="bg-white rounded-lg p-4 shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <div className="font-semibold">Video Score</div>
          {!contextVideoScores && (
            <button
              onClick={analyzeVideo}
              disabled={isAnalyzingVideo}
              className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isAnalyzingVideo ? 'Analyzing...' : 'Analyze'}
            </button>
          )}
        </div>
        <div className="flex flex-col gap-4">
          <ScoreBar
            label="Professionalism"
            value={isAnalyzingVideo ? 0 : scores.professionalism}
            loading={isAnalyzingVideo}
          />
          <ScoreBar
            label="Energy Level"
            value={isAnalyzingVideo ? 0 : scores.energyLevel}
            color="bg-purple-600"
            loading={isAnalyzingVideo}
          />
          <ScoreBar
            label="Communication"
            value={isAnalyzingVideo ? 0 : scores.communication}
            loading={isAnalyzingVideo}
          />
          <ScoreBar
            label="Sociability"
            value={isAnalyzingVideo ? 0 : scores.sociability}
            loading={isAnalyzingVideo}
          />
        </div>
        
      </div>

      {/* AI Ratings */}
      <div className="bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm">
        <p className="font-semibold">AI Rating</p>
        <CircularRating
          label="AI Resume Rating"
          percent={75}
          color="#A855F7"
          trailColor="#EAE2FF"
        />
        <CircularRating
          label="AI Video Rating"
          percent={75}
          color="#FF5B00"
          trailColor="#FFEAE1"
        />
      </div>
    </div>
  );
};

export default ScoreCard;
