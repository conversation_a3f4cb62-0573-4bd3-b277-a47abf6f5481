export { auth as middleware } from "@/auth";

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - job-posts (allow access without auth)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|job-posts|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
