"use client";
import React, { useState } from "react";
import InterviewInstructions from "@/components/interview/InterviewInstructions";
import InterviewWithDID from "@/components/interview/InterviewWithDID";
import FinishInterview from "@/components/interview/FinishInterview";
import Analysis from "@/components/interview/Analysis";
import { InterviewProvider } from "@/context/InterviewContext";

type InterviewStep =
  | "instructions"
  | "questions"
  | "recording"
  | "finishInterview"
  | "analysis";

const Interview = () => {
  const [currentStep, setCurrentStep] = useState<InterviewStep>("instructions");

  const renderCurrentComponent = () => {
    switch (currentStep) {
      case "instructions":
        return (
          <InterviewInstructions mode="instructions" onNext={() => setCurrentStep("questions")} />
        );
      case "questions":
        return <InterviewWithDID onNext={() => setCurrentStep("finishInterview")} />;
      case "finishInterview":
        return <FinishInterview onNext={() => setCurrentStep("analysis")} />;
      case "analysis":
        return <Analysis />;
      default:
        return (
          <InterviewInstructions mode="instructions" onNext={() => setCurrentStep("questions")} />
        );
    }
  };

  return (
    <InterviewProvider>
      <div>{renderCurrentComponent()}</div>
    </InterviewProvider>
  );
};

export default Interview;
